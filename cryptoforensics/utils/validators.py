"""
Input validation utilities for CryptoForensics

Provides comprehensive validation for cryptocurrency addresses, transaction IDs,
and other input data with support for multiple blockchain networks.
"""

import re
import hashlib
import base58
import logging
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass

from .. import ValidationError

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of a validation operation."""
    is_valid: bool
    address_type: Optional[str] = None
    network: Optional[str] = None
    error_message: Optional[str] = None
    confidence: float = 1.0
    additional_info: Dict[str, any] = None

class AddressValidator:
    """
    Comprehensive cryptocurrency address validator.
    
    Supports validation for multiple blockchain networks including Bitcoin,
    Ethereum, Litecoin, and Bitcoin Cash with detailed error reporting.
    """
    
    def __init__(self):
        """Initialize the address validator."""
        self.supported_networks = {
            "bitcoin": self._validate_bitcoin_address,
            "ethereum": self._validate_ethereum_address,
            "litecoin": self._validate_litecoin_address,
            "bitcoin_cash": self._validate_bitcoin_cash_address
        }
        
        # Bitcoin address patterns
        self.bitcoin_patterns = {
            "legacy_p2pkh": r'^[1][a-km-zA-HJ-NP-Z1-9]{25,34}$',
            "legacy_p2sh": r'^[3][a-km-zA-HJ-NP-Z1-9]{25,34}$',
            "bech32_p2wpkh": r'^bc1[a-z0-9]{39,59}$',
            "bech32_p2wsh": r'^bc1[a-z0-9]{59}$',
            "taproot": r'^bc1p[a-z0-9]{58}$'
        }
        
        # Ethereum address pattern
        self.ethereum_pattern = r'^0x[a-fA-F0-9]{40}$'
        
        logger.debug("Address validator initialized")
    
    def validate_address(self, address: str, network: str = "bitcoin") -> ValidationResult:
        """
        Validate a cryptocurrency address.
        
        Args:
            address: Address string to validate
            network: Blockchain network (bitcoin, ethereum, etc.)
            
        Returns:
            ValidationResult with detailed validation information
        """
        if not address:
            return ValidationResult(
                is_valid=False,
                error_message="Address cannot be empty"
            )
        
        if network not in self.supported_networks:
            return ValidationResult(
                is_valid=False,
                error_message=f"Unsupported network: {network}"
            )
        
        try:
            return self.supported_networks[network](address)
        except Exception as e:
            logger.error(f"Address validation failed: {e}")
            return ValidationResult(
                is_valid=False,
                error_message=f"Validation error: {str(e)}"
            )
    
    def _validate_bitcoin_address(self, address: str) -> ValidationResult:
        """Validate Bitcoin address with detailed type detection."""
        # Check basic format first
        if len(address) < 26 or len(address) > 62:
            return ValidationResult(
                is_valid=False,
                error_message="Invalid Bitcoin address length"
            )
        
        # Check against known patterns
        for addr_type, pattern in self.bitcoin_patterns.items():
            if re.match(pattern, address):
                # Perform additional validation based on type
                if addr_type in ["legacy_p2pkh", "legacy_p2sh"]:
                    return self._validate_legacy_bitcoin(address, addr_type)
                elif addr_type.startswith("bech32"):
                    return self._validate_bech32_bitcoin(address, addr_type)
                elif addr_type == "taproot":
                    return self._validate_taproot_bitcoin(address)
        
        return ValidationResult(
            is_valid=False,
            error_message="Invalid Bitcoin address format"
        )
    
    def _validate_legacy_bitcoin(self, address: str, addr_type: str) -> ValidationResult:
        """Validate legacy Bitcoin addresses with checksum verification."""
        try:
            # Decode base58 and verify checksum
            decoded = base58.b58decode(address)
            
            if len(decoded) != 25:
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid legacy address length"
                )
            
            # Verify checksum
            payload = decoded[:-4]
            checksum = decoded[-4:]
            hash_result = hashlib.sha256(hashlib.sha256(payload).digest()).digest()
            
            if hash_result[:4] != checksum:
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid checksum"
                )
            
            # Verify version byte
            version = payload[0]
            if addr_type == "legacy_p2pkh" and version != 0x00:
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid P2PKH version byte"
                )
            elif addr_type == "legacy_p2sh" and version != 0x05:
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid P2SH version byte"
                )
            
            return ValidationResult(
                is_valid=True,
                address_type=addr_type,
                network="bitcoin",
                confidence=1.0,
                additional_info={"version_byte": version}
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                error_message=f"Legacy address validation failed: {str(e)}"
            )
    
    def _validate_bech32_bitcoin(self, address: str, addr_type: str) -> ValidationResult:
        """Validate Bech32 Bitcoin addresses."""
        try:
            # Basic Bech32 validation
            if not address.startswith('bc1'):
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid Bech32 prefix"
                )
            
            # Check character set
            valid_chars = set('qpzry9x8gf2tvdw0s3jn54khce6mua7l')
            address_chars = set(address[3:].lower())
            
            if not address_chars.issubset(valid_chars):
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid Bech32 characters"
                )
            
            # Length validation
            if addr_type == "bech32_p2wpkh" and len(address) != 42:
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid P2WPKH length"
                )
            elif addr_type == "bech32_p2wsh" and len(address) != 62:
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid P2WSH length"
                )
            
            return ValidationResult(
                is_valid=True,
                address_type=addr_type,
                network="bitcoin",
                confidence=0.95,  # Slightly lower due to simplified validation
                additional_info={"witness_version": 0}
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                error_message=f"Bech32 validation failed: {str(e)}"
            )
    
    def _validate_taproot_bitcoin(self, address: str) -> ValidationResult:
        """Validate Taproot Bitcoin addresses."""
        try:
            if not address.startswith('bc1p'):
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid Taproot prefix"
                )
            
            if len(address) != 62:
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid Taproot length"
                )
            
            # Check character set (Bech32m)
            valid_chars = set('qpzry9x8gf2tvdw0s3jn54khce6mua7l')
            address_chars = set(address[4:].lower())
            
            if not address_chars.issubset(valid_chars):
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid Taproot characters"
                )
            
            return ValidationResult(
                is_valid=True,
                address_type="taproot",
                network="bitcoin",
                confidence=0.95,
                additional_info={"witness_version": 1}
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                error_message=f"Taproot validation failed: {str(e)}"
            )
    
    def _validate_ethereum_address(self, address: str) -> ValidationResult:
        """Validate Ethereum address with checksum verification."""
        if not re.match(self.ethereum_pattern, address):
            return ValidationResult(
                is_valid=False,
                error_message="Invalid Ethereum address format"
            )
        
        # Check EIP-55 checksum if mixed case
        if address != address.lower() and address != address.upper():
            if not self._verify_ethereum_checksum(address):
                return ValidationResult(
                    is_valid=False,
                    error_message="Invalid EIP-55 checksum"
                )
        
        return ValidationResult(
            is_valid=True,
            address_type="ethereum",
            network="ethereum",
            confidence=1.0,
            additional_info={"has_checksum": address != address.lower() and address != address.upper()}
        )
    
    def _verify_ethereum_checksum(self, address: str) -> bool:
        """Verify EIP-55 checksum for Ethereum address."""
        try:
            address = address[2:]  # Remove 0x prefix
            address_hash = hashlib.sha3_256(address.lower().encode()).hexdigest()
            
            for i, char in enumerate(address):
                if char.isalpha():
                    if int(address_hash[i], 16) >= 8:
                        if char != char.upper():
                            return False
                    else:
                        if char != char.lower():
                            return False
            
            return True
        except:
            return False
    
    def _validate_litecoin_address(self, address: str) -> ValidationResult:
        """Validate Litecoin address."""
        # Litecoin uses similar format to Bitcoin but different version bytes
        if address.startswith('L') or address.startswith('M'):
            # Legacy Litecoin addresses
            try:
                decoded = base58.b58decode(address)
                if len(decoded) != 25:
                    return ValidationResult(is_valid=False, error_message="Invalid length")
                
                version = decoded[0]
                if (address.startswith('L') and version != 0x30) or \
                   (address.startswith('M') and version != 0x32):
                    return ValidationResult(is_valid=False, error_message="Invalid version")
                
                return ValidationResult(
                    is_valid=True,
                    address_type="litecoin_legacy",
                    network="litecoin",
                    confidence=1.0
                )
            except:
                return ValidationResult(is_valid=False, error_message="Decode failed")
        
        elif address.startswith('ltc1'):
            # Litecoin Bech32
            return ValidationResult(
                is_valid=True,
                address_type="litecoin_bech32",
                network="litecoin",
                confidence=0.9
            )
        
        return ValidationResult(is_valid=False, error_message="Invalid Litecoin format")
    
    def _validate_bitcoin_cash_address(self, address: str) -> ValidationResult:
        """Validate Bitcoin Cash address."""
        # Bitcoin Cash can use legacy format or CashAddr format
        if address.startswith('bitcoincash:') or address.startswith('1') or address.startswith('3'):
            return ValidationResult(
                is_valid=True,
                address_type="bitcoin_cash",
                network="bitcoin_cash",
                confidence=0.8  # Simplified validation
            )
        
        return ValidationResult(is_valid=False, error_message="Invalid Bitcoin Cash format")
    
    def detect_address_network(self, address: str) -> List[ValidationResult]:
        """
        Detect which networks an address might belong to.
        
        Args:
            address: Address to analyze
            
        Returns:
            List of possible validation results for different networks
        """
        results = []
        
        for network in self.supported_networks:
            result = self.validate_address(address, network)
            if result.is_valid:
                results.append(result)
        
        # Sort by confidence
        results.sort(key=lambda x: x.confidence, reverse=True)
        return results

class TransactionValidator:
    """
    Transaction ID and transaction data validator.
    
    Provides validation for transaction IDs across multiple blockchain networks
    and validation of transaction data structures.
    """
    
    def __init__(self):
        """Initialize the transaction validator."""
        self.network_patterns = {
            "bitcoin": r'^[a-fA-F0-9]{64}$',
            "ethereum": r'^0x[a-fA-F0-9]{64}$',
            "litecoin": r'^[a-fA-F0-9]{64}$',
            "bitcoin_cash": r'^[a-fA-F0-9]{64}$'
        }
        
        logger.debug("Transaction validator initialized")
    
    def validate_transaction_id(self, txid: str, network: str = "bitcoin") -> ValidationResult:
        """
        Validate a transaction ID.
        
        Args:
            txid: Transaction ID to validate
            network: Blockchain network
            
        Returns:
            ValidationResult with validation details
        """
        if not txid:
            return ValidationResult(
                is_valid=False,
                error_message="Transaction ID cannot be empty"
            )
        
        if network not in self.network_patterns:
            return ValidationResult(
                is_valid=False,
                error_message=f"Unsupported network: {network}"
            )
        
        pattern = self.network_patterns[network]
        
        if not re.match(pattern, txid):
            return ValidationResult(
                is_valid=False,
                error_message=f"Invalid {network} transaction ID format"
            )
        
        # Additional validation based on network
        if network == "ethereum" and not txid.startswith('0x'):
            return ValidationResult(
                is_valid=False,
                error_message="Ethereum transaction ID must start with 0x"
            )
        
        return ValidationResult(
            is_valid=True,
            network=network,
            confidence=1.0,
            additional_info={"length": len(txid)}
        )
    
    def validate_transaction_data(self, tx_data: Dict) -> ValidationResult:
        """
        Validate transaction data structure.
        
        Args:
            tx_data: Transaction data dictionary
            
        Returns:
            ValidationResult with validation details
        """
        required_fields = ["txid", "vin", "vout"]
        missing_fields = []
        
        for field in required_fields:
            if field not in tx_data:
                missing_fields.append(field)
        
        if missing_fields:
            return ValidationResult(
                is_valid=False,
                error_message=f"Missing required fields: {', '.join(missing_fields)}"
            )
        
        # Validate transaction ID
        txid_result = self.validate_transaction_id(tx_data["txid"])
        if not txid_result.is_valid:
            return ValidationResult(
                is_valid=False,
                error_message=f"Invalid transaction ID: {txid_result.error_message}"
            )
        
        # Validate inputs and outputs
        if not isinstance(tx_data["vin"], list):
            return ValidationResult(
                is_valid=False,
                error_message="Transaction inputs must be a list"
            )
        
        if not isinstance(tx_data["vout"], list):
            return ValidationResult(
                is_valid=False,
                error_message="Transaction outputs must be a list"
            )
        
        if len(tx_data["vout"]) == 0:
            return ValidationResult(
                is_valid=False,
                error_message="Transaction must have at least one output"
            )
        
        return ValidationResult(
            is_valid=True,
            confidence=1.0,
            additional_info={
                "input_count": len(tx_data["vin"]),
                "output_count": len(tx_data["vout"])
            }
        )
    
    def detect_transaction_network(self, txid: str) -> List[ValidationResult]:
        """
        Detect which networks a transaction ID might belong to.
        
        Args:
            txid: Transaction ID to analyze
            
        Returns:
            List of possible validation results for different networks
        """
        results = []
        
        for network in self.network_patterns:
            result = self.validate_transaction_id(txid, network)
            if result.is_valid:
                results.append(result)
        
        return results
