"""
Advanced API client for CryptoForensics

Provides robust API interaction with rate limiting, connection pooling,
caching, and comprehensive error handling for blockchain APIs.
"""

import asyncio
import aiohttp
import time
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import json
import hashlib
from dataclasses import dataclass
from collections import defaultdict
import ssl

from ..core.config import APIConfig
from .. import APIError

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry for API responses."""
    data: Any
    timestamp: datetime
    ttl: int
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        return datetime.now() > self.timestamp + timedelta(seconds=self.ttl)

class RateLimiter:
    """Advanced rate limiter with burst support."""
    
    def __init__(self, requests_per_minute: int = 60, burst_limit: int = 10):
        """
        Initialize rate limiter.
        
        Args:
            requests_per_minute: Maximum requests per minute
            burst_limit: Maximum burst requests
        """
        self.requests_per_minute = requests_per_minute
        self.burst_limit = burst_limit
        self.request_times = []
        self.burst_count = 0
        self.last_reset = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire permission to make a request."""
        async with self._lock:
            now = time.time()
            
            # Reset burst counter every minute
            if now - self.last_reset > 60:
                self.burst_count = 0
                self.last_reset = now
            
            # Remove old request times
            cutoff = now - 60  # 1 minute ago
            self.request_times = [t for t in self.request_times if t > cutoff]
            
            # Check rate limits
            if len(self.request_times) >= self.requests_per_minute:
                sleep_time = 60 - (now - self.request_times[0])
                if sleep_time > 0:
                    logger.debug(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                    await asyncio.sleep(sleep_time)
            
            # Check burst limit
            if self.burst_count >= self.burst_limit:
                await asyncio.sleep(1)  # Brief pause for burst control
            
            # Record this request
            self.request_times.append(now)
            self.burst_count += 1

class APIClient:
    """
    Professional API client for blockchain data retrieval.
    
    Features:
    - Async/await support for high performance
    - Intelligent rate limiting with burst support
    - Response caching with TTL
    - Connection pooling and reuse
    - Comprehensive error handling and retry logic
    - Multiple API endpoint support
    """
    
    def __init__(self, config: APIConfig):
        """
        Initialize API client.
        
        Args:
            config: API configuration
        """
        self.config = config
        self.rate_limiter = RateLimiter(
            requests_per_minute=config.requests_per_minute,
            burst_limit=config.burst_limit
        )
        
        # Response cache
        self.cache: Dict[str, CacheEntry] = {}
        self.cache_enabled = config.enable_caching
        
        # Performance metrics
        self.metrics = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0,
            "retries": 0,
            "total_response_time": 0.0
        }
        
        # Session will be created when needed
        self.session: Optional[aiohttp.ClientSession] = None
        
        logger.info("API client initialized")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session with connection pooling."""
        if self.session is None or self.session.closed:
            # Configure SSL context
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # Configure connector with connection pooling
            connector = aiohttp.TCPConnector(
                limit=self.config.connection_pool_size,
                limit_per_host=self.config.connection_pool_size // 2,
                ttl_dns_cache=300,
                use_dns_cache=True,
                ssl=ssl_context
            )
            
            # Configure timeout
            timeout = aiohttp.ClientTimeout(
                total=self.config.request_timeout,
                connect=10,
                sock_read=self.config.request_timeout
            )
            
            # Create session
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'CryptoForensics/3.0.0',
                    'Accept': 'application/json',
                    'Accept-Encoding': 'gzip, deflate'
                }
            )
            
            logger.debug("Created new aiohttp session")
        
        return self.session
    
    def _get_cache_key(self, url: str, params: Optional[Dict] = None) -> str:
        """Generate cache key for request."""
        key_data = f"{url}:{json.dumps(params or {}, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cached_response(self, cache_key: str) -> Optional[Any]:
        """Get cached response if available and not expired."""
        if not self.cache_enabled or cache_key not in self.cache:
            return None
        
        entry = self.cache[cache_key]
        if entry.is_expired():
            del self.cache[cache_key]
            return None
        
        self.metrics["cache_hits"] += 1
        logger.debug(f"Cache hit for key: {cache_key[:8]}...")
        return entry.data
    
    def _cache_response(self, cache_key: str, data: Any, ttl: Optional[int] = None) -> None:
        """Cache response data."""
        if not self.cache_enabled:
            return
        
        ttl = ttl or self.config.cache_ttl
        self.cache[cache_key] = CacheEntry(
            data=data,
            timestamp=datetime.now(),
            ttl=ttl
        )
        
        # Cleanup old cache entries periodically
        if len(self.cache) > 1000:
            self._cleanup_cache()
    
    def _cleanup_cache(self) -> None:
        """Remove expired cache entries."""
        expired_keys = [
            key for key, entry in self.cache.items()
            if entry.is_expired()
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    async def _make_request(self, 
                           url: str, 
                           params: Optional[Dict] = None,
                           cache_ttl: Optional[int] = None) -> Optional[Dict]:
        """
        Make HTTP request with rate limiting, caching, and error handling.
        
        Args:
            url: Request URL
            params: Query parameters
            cache_ttl: Cache TTL override
            
        Returns:
            Response data or None if failed
        """
        # Check cache first
        cache_key = self._get_cache_key(url, params)
        cached_response = self._get_cached_response(cache_key)
        if cached_response is not None:
            return cached_response
        
        self.metrics["cache_misses"] += 1
        
        # Apply rate limiting
        await self.rate_limiter.acquire()
        
        session = await self._get_session()
        start_time = time.time()
        
        for attempt in range(self.config.max_retries):
            try:
                self.metrics["total_requests"] += 1
                
                async with session.get(url, params=params) as response:
                    response_time = time.time() - start_time
                    self.metrics["total_response_time"] += response_time
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        # Cache successful response
                        self._cache_response(cache_key, data, cache_ttl)
                        
                        logger.debug(f"Successful request to {url} in {response_time:.2f}s")
                        return data
                    
                    elif response.status == 404:
                        logger.warning(f"Resource not found: {url}")
                        return None
                    
                    elif response.status == 429:
                        # Rate limited - exponential backoff
                        retry_after = int(response.headers.get('Retry-After', 2 ** attempt))
                        logger.warning(f"Rate limited, waiting {retry_after}s before retry")
                        await asyncio.sleep(retry_after)
                        self.metrics["retries"] += 1
                        continue
                    
                    else:
                        logger.error(f"HTTP {response.status} for {url}")
                        if attempt < self.config.max_retries - 1:
                            await asyncio.sleep(2 ** attempt)
                            self.metrics["retries"] += 1
                            continue
                        else:
                            self.metrics["errors"] += 1
                            return None
            
            except asyncio.TimeoutError:
                logger.warning(f"Timeout for {url}, attempt {attempt + 1}")
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    self.metrics["retries"] += 1
                else:
                    self.metrics["errors"] += 1
                    return None
            
            except Exception as e:
                logger.error(f"Request error for {url}: {e}")
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    self.metrics["retries"] += 1
                else:
                    self.metrics["errors"] += 1
                    return None
        
        return None
    
    async def get_transaction_async(self, txid: str) -> Optional[Dict]:
        """
        Get transaction data asynchronously.
        
        Args:
            txid: Transaction ID
            
        Returns:
            Transaction data or None if not found
        """
        url = f"{self.config.base_url}tx/{txid}"
        return await self._make_request(url)
    
    async def get_outspend_async(self, txid: str, vout_index: int) -> Optional[Dict]:
        """
        Get outspend data asynchronously.
        
        Args:
            txid: Transaction ID
            vout_index: Output index
            
        Returns:
            Outspend data or None if not found
        """
        url = f"{self.config.base_url}tx/{txid}/outspend/{vout_index}"
        return await self._make_request(url)
    
    async def get_address_info_async(self, address: str) -> Optional[Dict]:
        """
        Get address information asynchronously.
        
        Args:
            address: Bitcoin address
            
        Returns:
            Address information or None if not found
        """
        url = f"{self.config.base_url}address/{address}"
        return await self._make_request(url)
    
    async def get_address_transactions_async(self, 
                                           address: str, 
                                           last_seen_txid: Optional[str] = None) -> Optional[List[Dict]]:
        """
        Get address transactions asynchronously.
        
        Args:
            address: Bitcoin address
            last_seen_txid: Last seen transaction ID for pagination
            
        Returns:
            List of transactions or None if error
        """
        url = f"{self.config.base_url}address/{address}/txs"
        params = {}
        if last_seen_txid:
            params['after_txid'] = last_seen_txid
        
        return await self._make_request(url, params)
    
    async def get_block_info_async(self, block_hash: str) -> Optional[Dict]:
        """
        Get block information asynchronously.
        
        Args:
            block_hash: Block hash
            
        Returns:
            Block information or None if not found
        """
        url = f"{self.config.base_url}block/{block_hash}"
        return await self._make_request(url)
    
    async def get_mempool_info_async(self) -> Optional[Dict]:
        """
        Get mempool information asynchronously.
        
        Returns:
            Mempool information or None if error
        """
        url = f"{self.config.base_url}mempool"
        return await self._make_request(url, cache_ttl=60)  # Short cache for mempool data
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get API client performance metrics."""
        total_requests = max(self.metrics["total_requests"], 1)
        
        return {
            "total_requests": self.metrics["total_requests"],
            "cache_hit_rate": self.metrics["cache_hits"] / (self.metrics["cache_hits"] + self.metrics["cache_misses"]) if (self.metrics["cache_hits"] + self.metrics["cache_misses"]) > 0 else 0,
            "error_rate": self.metrics["errors"] / total_requests,
            "retry_rate": self.metrics["retries"] / total_requests,
            "average_response_time": self.metrics["total_response_time"] / total_requests,
            "cache_size": len(self.cache),
            "session_active": self.session is not None and not self.session.closed
        }
    
    async def close(self) -> None:
        """Close the API client and cleanup resources."""
        if self.session and not self.session.closed:
            await self.session.close()
            logger.debug("API client session closed")
        
        # Clear cache
        self.cache.clear()
        
        logger.info("API client closed")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
