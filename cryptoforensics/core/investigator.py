"""
Main investigator class for CryptoForensics

Provides the core investigation functionality with enhanced error handling,
async support, and professional-grade features.
"""

import asyncio
import uuid
import logging
from typing import Dict, List, Optional, Tuple, Set, Any, Union
from datetime import datetime
from pathlib import Path
import networkx as nx
from dataclasses import dataclass, asdict

from .config import InvestigationConfig, GlobalConfig
from ..evidence.collector import EvidenceCollector
from ..evidence.audit_trail import AuditTrail
from ..analysis.pattern_analyzer import <PERSON><PERSON><PERSON>nal<PERSON><PERSON>
from ..analysis.risk_assessor import RiskAssessor
from ..analysis.suspicious_activity import SuspiciousActivityDetector
from ..utils.validators import AddressValidator, TransactionValidator
from ..utils.api_client import APIClient
from ..visualization.graph_generator import GraphGenerator
from ..reporting.report_generator import ReportGenerator
from ..exceptions import CryptoForensicsError, ValidationError, AnalysisError

logger = logging.getLogger(__name__)

@dataclass
class TransactionInfo:
    """Enhanced transaction information with additional metadata."""
    txid: str
    from_address: str
    to_address: str
    amount_btc: float
    depth: int
    timestamp: str
    block_height: Optional[int] = None
    confirmations: bool = False
    investigation_id: str = ""
    network: str = "bitcoin"
    fee: Optional[float] = None
    input_count: int = 0
    output_count: int = 0
    is_coinbase: bool = False
    confidence_score: float = 1.0
    tags: List[str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class InvestigationResult:
    """Comprehensive investigation result container."""
    investigation_id: str
    timestamp: str
    input_parameters: Dict[str, Any]
    basic_results: Dict[str, Any]
    graph: nx.DiGraph
    detailed_transactions: List[TransactionInfo]
    advanced_analysis: Dict[str, Any]
    investigation_summary: Dict[str, Any]
    evidence_items: List[Any]
    audit_trail: List[Any]
    performance_metrics: Dict[str, Any]
    quality_assessment: Dict[str, Any]

class CryptoForensicsInvestigator:
    """
    Professional cryptocurrency forensics investigation platform.

    This class provides comprehensive functionality for tracing cryptocurrency transactions,
    analyzing fund flows, detecting suspicious patterns, and generating legal-grade
    evidence packages with proper chain of custody.
    """

    def __init__(self,
                 config: Optional[InvestigationConfig] = None,
                 global_config: Optional[GlobalConfig] = None):
        """
        Initialize the investigator with configuration.

        Args:
            config: Investigation-specific configuration
            global_config: Global application configuration
        """
        self.global_config = global_config or GlobalConfig()
        self.config = config or self.global_config.investigation
        self.investigation_id = str(uuid.uuid4())

        # Initialize components
        self._initialize_components()

        # Performance tracking
        self.start_time = datetime.now()
        self.performance_metrics = {
            "api_calls": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0,
            "warnings": 0,
        }

        # Create output directory
        Path(self.config.output_directory).mkdir(parents=True, exist_ok=True)

        # Log initialization
        self.audit_trail.log_event("investigation_initialized", {
            "investigation_id": self.investigation_id,
            "config": self.config.model_dump(),
            "timestamp": self.start_time.isoformat()
        })

        logger.info(f"Initialized CryptoForensics Investigator (ID: {self.investigation_id})")

    def _initialize_components(self) -> None:
        """Initialize all investigator components."""
        try:
            # Core components
            self.api_client = APIClient(self.global_config.api)
            self.evidence_collector = EvidenceCollector(self.investigation_id, self.config)
            self.audit_trail = AuditTrail(self.investigation_id, self.config)

            # Validators
            self.address_validator = AddressValidator()
            self.transaction_validator = TransactionValidator()

            # Analysis components
            self.pattern_analyzer = PatternAnalyzer(self.config)
            self.risk_assessor = RiskAssessor(self.config)
            self.suspicious_activity_detector = SuspiciousActivityDetector(self.config)

            # Output components
            self.graph_generator = GraphGenerator(self.config)
            self.report_generator = ReportGenerator(self.config, self.investigation_id)

            logger.debug("All investigator components initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize investigator components: {e}")
            raise CryptoForensicsError(f"Initialization failed: {e}")

    async def trace_funds_async(self,
                               initial_txid: str,
                               target_address: str,
                               max_depth: Optional[int] = None) -> Tuple[nx.DiGraph, List[TransactionInfo]]:
        """
        Asynchronously trace cryptocurrency funds with enhanced performance.

        Args:
            initial_txid: Starting transaction ID
            target_address: Address that received the initial funds
            max_depth: Maximum tracing depth (uses config default if None)

        Returns:
            Tuple of (transaction graph, transaction list)

        Raises:
            ValidationError: If input parameters are invalid
            CryptoForensicsError: If tracing fails
        """
        if max_depth is None:
            max_depth = self.config.max_depth

        # Validate inputs
        if not self.transaction_validator.validate_transaction_id(initial_txid):
            raise ValidationError(f"Invalid transaction ID: {initial_txid}")

        if not self.address_validator.validate_address(target_address):
            raise ValidationError(f"Invalid address: {target_address}")

        logger.info(f"Starting async fund tracing from TXID: {initial_txid}")

        # Log investigation start
        self.audit_trail.log_event("async_tracing_started", {
            "initial_txid": initial_txid,
            "target_address": target_address,
            "max_depth": max_depth
        })

        try:
            # Initialize tracking structures
            graph = nx.DiGraph()
            transactions = []
            visited_txs = {initial_txid}
            visited_addresses = set()

            # Add initial address to graph
            graph.add_node(target_address,
                          label=f"Initial Address:\n{target_address[:8]}...",
                          address_type="initial",
                          first_seen=datetime.now().isoformat())

            # Use async queue for processing
            queue = asyncio.Queue()
            await queue.put((initial_txid, target_address, 0))

            # Process transactions asynchronously
            while not queue.empty() and len(transactions) < self.config.max_transactions:
                current_txid, source_address, depth = await queue.get()

                if depth >= max_depth:
                    continue

                # Get transaction data asynchronously
                tx_data = await self.api_client.get_transaction_async(current_txid)
                if not tx_data:
                    self.performance_metrics["errors"] += 1
                    continue

                self.performance_metrics["api_calls"] += 1

                # Process transaction outputs
                new_transactions = await self._process_transaction_outputs_async(
                    tx_data, source_address, depth, visited_txs, visited_addresses, graph, queue
                )

                transactions.extend(new_transactions)

                # Check limits
                if len(transactions) >= self.config.max_transactions:
                    logger.warning(f"Reached maximum transaction limit: {self.config.max_transactions}")
                    break

                if len(graph.nodes) >= self.config.max_addresses:
                    logger.warning(f"Reached maximum address limit: {self.config.max_addresses}")
                    break

            # Log completion
            self.audit_trail.log_event("async_tracing_completed", {
                "transactions_found": len(transactions),
                "addresses_discovered": len(graph.nodes),
                "total_amount_traced": sum(tx.amount_btc for tx in transactions) if transactions else 0
            })

            logger.info(f"Async tracing completed: {len(transactions)} transactions, {len(graph.nodes)} addresses")
            return graph, transactions

        except Exception as e:
            self.performance_metrics["errors"] += 1
            logger.error(f"Async tracing failed: {e}")
            raise CryptoForensicsError(f"Fund tracing failed: {e}")

    def trace_funds(self,
                   initial_txid: str,
                   target_address: str,
                   max_depth: Optional[int] = None) -> Tuple[nx.DiGraph, List[TransactionInfo]]:
        """
        Synchronous wrapper for async fund tracing.

        Args:
            initial_txid: Starting transaction ID
            target_address: Address that received the initial funds
            max_depth: Maximum tracing depth

        Returns:
            Tuple of (transaction graph, transaction list)
        """
        return asyncio.run(self.trace_funds_async(initial_txid, target_address, max_depth))

    async def comprehensive_investigation_async(self,
                                              initial_txid: str,
                                              target_address: str,
                                              max_depth: Optional[int] = None) -> InvestigationResult:
        """
        Perform a comprehensive forensic investigation asynchronously.

        Args:
            initial_txid: Starting transaction ID
            target_address: Address that received the initial funds
            max_depth: Maximum tracing depth

        Returns:
            Comprehensive investigation results
        """
        logger.info("Starting comprehensive forensic investigation")

        start_time = datetime.now()

        try:
            # Perform basic transaction tracing
            graph, transactions = await self.trace_funds_async(initial_txid, target_address, max_depth)

            # Perform advanced analysis if we have data
            advanced_analysis = {}
            if transactions:
                logger.info("Performing advanced pattern analysis")

                # Run analysis components
                if self.config.enable_pattern_analysis:
                    pattern_results = await self.pattern_analyzer.analyze_async(transactions)
                    advanced_analysis["transaction_patterns"] = pattern_results

                if self.config.enable_risk_assessment:
                    risk_results = await self.risk_assessor.assess_async(transactions)
                    advanced_analysis["risk_assessment"] = risk_results

                suspicious_results = await self.suspicious_activity_detector.detect_async(transactions)
                advanced_analysis["suspicious_activity"] = suspicious_results

                # Create evidence items for analysis results
                for analysis_type, results in advanced_analysis.items():
                    self.evidence_collector.create_evidence_item(
                        evidence_type=f"{analysis_type}_analysis",
                        description=f"{analysis_type.replace('_', ' ').title()} analysis results",
                        data=results
                    )

            # Calculate performance metrics
            end_time = datetime.now()
            self.performance_metrics.update({
                "total_duration": (end_time - start_time).total_seconds(),
                "transactions_per_second": len(transactions) / max((end_time - start_time).total_seconds(), 1),
                "api_calls_per_transaction": self.performance_metrics["api_calls"] / max(len(transactions), 1),
            })

            # Generate investigation summary
            investigation_summary = self._generate_investigation_summary(transactions, advanced_analysis)

            # Create comprehensive result
            result = InvestigationResult(
                investigation_id=self.investigation_id,
                timestamp=end_time.isoformat(),
                input_parameters={
                    "initial_txid": initial_txid,
                    "target_address": target_address,
                    "max_depth": max_depth or self.config.max_depth
                },
                basic_results={
                    "transaction_count": len(transactions),
                    "address_count": len(graph.nodes),
                    "total_amount": sum(tx.amount_btc for tx in transactions) if transactions else 0
                },
                graph=graph,
                detailed_transactions=transactions,
                advanced_analysis=advanced_analysis,
                investigation_summary=investigation_summary,
                evidence_items=self.evidence_collector.get_all_evidence(),
                audit_trail=self.audit_trail.get_all_events(),
                performance_metrics=self.performance_metrics,
                quality_assessment=self._assess_investigation_quality(transactions)
            )

            logger.info("Comprehensive investigation completed successfully")
            return result

        except Exception as e:
            logger.error(f"Comprehensive investigation failed: {e}")
            raise CryptoForensicsError(f"Investigation failed: {e}")

    def comprehensive_investigation(self,
                                  initial_txid: str,
                                  target_address: str,
                                  max_depth: Optional[int] = None) -> InvestigationResult:
        """
        Synchronous wrapper for comprehensive investigation.

        Args:
            initial_txid: Starting transaction ID
            target_address: Address that received the initial funds
            max_depth: Maximum tracing depth

        Returns:
            Comprehensive investigation results
        """
        return asyncio.run(self.comprehensive_investigation_async(initial_txid, target_address, max_depth))

    async def _process_transaction_outputs_async(self,
                                               tx_data: Dict[str, Any],
                                               source_address: str,
                                               depth: int,
                                               visited_txs: Set[str],
                                               visited_addresses: Set[str],
                                               graph: nx.DiGraph,
                                               queue: asyncio.Queue) -> List[TransactionInfo]:
        """Process transaction outputs asynchronously."""
        new_transactions = []

        try:
            # Find outputs that go to the source address
            source_outputs = []
            for i, vout in enumerate(tx_data.get('vout', [])):
                output_address = vout.get('scriptpubkey_address')
                if output_address == source_address:
                    source_outputs.append((i, vout))

            # Process each relevant output
            for vout_index, vout in source_outputs:
                # Get spending transaction asynchronously
                outspend_data = await self.api_client.get_outspend_async(tx_data['txid'], vout_index)
                if not outspend_data or not outspend_data.get('spent'):
                    continue

                spending_txid = outspend_data['txid']
                if spending_txid in visited_txs:
                    continue

                visited_txs.add(spending_txid)

                # Get spending transaction details
                spending_tx_data = await self.api_client.get_transaction_async(spending_txid)
                if not spending_tx_data:
                    continue

                # Process spending transaction outputs
                for next_vout in spending_tx_data.get('vout', []):
                    new_address = next_vout.get('scriptpubkey_address')
                    if not new_address or new_address == source_address:
                        continue

                    amount_btc = next_vout['value'] / 100_000_000

                    # Add to graph
                    if new_address not in visited_addresses:
                        graph.add_node(new_address,
                                     label=f"Address:\n{new_address[:8]}...\n{amount_btc:.8f} BTC",
                                     address_type="traced",
                                     first_seen=datetime.now().isoformat(),
                                     total_received=amount_btc)
                        visited_addresses.add(new_address)

                    graph.add_edge(source_address, new_address,
                                 label=f"{amount_btc:.8f} BTC",
                                 amount=amount_btc,
                                 txid=spending_txid,
                                 timestamp=datetime.now().isoformat())

                    # Create transaction info
                    transaction_info = TransactionInfo(
                        txid=spending_txid,
                        from_address=source_address,
                        to_address=new_address,
                        amount_btc=amount_btc,
                        depth=depth + 1,
                        timestamp=datetime.now().isoformat(),
                        block_height=spending_tx_data.get('status', {}).get('block_height'),
                        confirmations=spending_tx_data.get('status', {}).get('confirmed', False),
                        investigation_id=self.investigation_id,
                        input_count=len(spending_tx_data.get('vin', [])),
                        output_count=len(spending_tx_data.get('vout', [])),
                        fee=spending_tx_data.get('fee', 0) / 100_000_000 if spending_tx_data.get('fee') else None
                    )

                    new_transactions.append(transaction_info)

                    # Create evidence item
                    self.evidence_collector.create_evidence_item(
                        evidence_type="transaction",
                        description=f"Transaction {spending_txid}: {amount_btc:.8f} BTC from {source_address[:8]}... to {new_address[:8]}...",
                        data={
                            "transaction_info": asdict(transaction_info),
                            "raw_transaction_data": spending_tx_data,
                            "discovery_context": {
                                "parent_txid": tx_data['txid'],
                                "output_index": vout_index,
                                "discovery_depth": depth
                            }
                        }
                    )

                    # Add to queue for further processing
                    await queue.put((spending_txid, new_address, depth + 1))

        except Exception as e:
            logger.error(f"Error processing transaction outputs: {e}")
            self.performance_metrics["errors"] += 1

        return new_transactions

    def _generate_investigation_summary(self,
                                      transactions: List[TransactionInfo],
                                      analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a high-level investigation summary."""
        if not transactions:
            return {
                "status": "no_transactions_found",
                "message": "No transactions were traced from the initial parameters"
            }

        # Extract key findings
        total_amount = sum(tx.amount_btc for tx in transactions)
        unique_addresses = len(set(tx.to_address for tx in transactions))
        max_depth = max(tx.depth for tx in transactions)

        # Get risk level
        risk_level = "UNKNOWN"
        if "risk_assessment" in analysis:
            risk_level = analysis["risk_assessment"].get("final_risk_level", "UNKNOWN")

        # Identify key concerns
        key_concerns = []
        if "suspicious_activity" in analysis:
            suspicious = analysis["suspicious_activity"]
            for activity, details in suspicious.items():
                if isinstance(details, dict) and details.get("detected", False):
                    key_concerns.append(activity)

        return {
            "status": "investigation_completed",
            "key_metrics": {
                "total_transactions": len(transactions),
                "total_amount_btc": total_amount,
                "unique_addresses": unique_addresses,
                "maximum_depth": max_depth,
                "risk_level": risk_level
            },
            "key_concerns": key_concerns,
            "investigation_quality": self._assess_investigation_quality(transactions),
            "next_steps": self._suggest_next_steps(transactions, analysis)
        }

    def _assess_investigation_quality(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Assess the quality and completeness of the investigation."""
        quality_score = 0
        quality_factors = []

        if len(transactions) > 5:
            quality_score += 2
            quality_factors.append("sufficient_transaction_volume")

        # Check depth coverage
        max_depth = max(tx.depth for tx in transactions) if transactions else 0
        if max_depth >= 3:
            quality_score += 2
            quality_factors.append("good_depth_coverage")

        # Check for confirmed transactions
        confirmed_txs = sum(1 for tx in transactions if tx.confirmations)
        if confirmed_txs > len(transactions) * 0.8:
            quality_score += 1
            quality_factors.append("high_confirmation_rate")

        # Check data completeness
        complete_data = sum(1 for tx in transactions if tx.block_height and tx.fee is not None)
        if complete_data > len(transactions) * 0.7:
            quality_score += 1
            quality_factors.append("complete_transaction_data")

        quality_level = "HIGH" if quality_score >= 4 else "MEDIUM" if quality_score >= 2 else "LOW"

        return {
            "quality_score": quality_score,
            "quality_level": quality_level,
            "quality_factors": quality_factors,
            "completeness_percentage": min(100, (quality_score / 6) * 100),
            "data_integrity_score": self._calculate_data_integrity_score(transactions)
        }

    def _calculate_data_integrity_score(self, transactions: List[TransactionInfo]) -> float:
        """Calculate data integrity score based on transaction completeness."""
        if not transactions:
            return 0.0

        integrity_factors = []

        for tx in transactions:
            score = 0
            # Check required fields
            if tx.txid and len(tx.txid) == 64:
                score += 1
            if tx.from_address and tx.to_address:
                score += 1
            if tx.amount_btc > 0:
                score += 1
            if tx.timestamp:
                score += 1
            if tx.block_height:
                score += 1
            if tx.fee is not None:
                score += 1

            integrity_factors.append(score / 6)  # Normalize to 0-1

        return sum(integrity_factors) / len(integrity_factors)

    def _suggest_next_steps(self,
                          transactions: List[TransactionInfo],
                          analysis: Dict[str, Any]) -> List[str]:
        """Suggest next steps based on investigation results."""
        next_steps = []

        if not transactions:
            next_steps.append("Verify input parameters and try again")
            next_steps.append("Check if funds have been moved recently")
            next_steps.append("Consider expanding search to related addresses")
            return next_steps

        # Get the latest addresses for further investigation
        latest_addresses = [tx.to_address for tx in transactions if tx.depth == max(tx.depth for tx in transactions)]
        if latest_addresses:
            next_steps.append(f"Monitor latest addresses for new activity: {', '.join(latest_addresses[:3])}")

        # Risk-based suggestions
        risk_level = analysis.get("risk_assessment", {}).get("final_risk_level", "UNKNOWN")
        if risk_level in ["CRITICAL", "HIGH"]:
            next_steps.append("Contact law enforcement immediately")
            next_steps.append("Prepare detailed evidence package for legal proceedings")
            next_steps.append("Implement immediate monitoring on all identified addresses")
        elif risk_level == "MEDIUM":
            next_steps.append("Enhanced due diligence recommended")
            next_steps.append("Consider filing suspicious activity report")

        # Analysis-specific suggestions
        if "suspicious_activity" in analysis:
            suspicious = analysis["suspicious_activity"]
            if suspicious.get("mixing_services", {}).get("detected", False):
                next_steps.append("Investigate potential mixing service usage further")
            if suspicious.get("exchange_deposits", {}).get("detected", False):
                next_steps.append("Contact identified exchanges for cooperation")

        next_steps.append("Set up automated monitoring alerts for identified addresses")
        next_steps.append("Consider expanding investigation depth if resources permit")
        next_steps.append("Review and validate all evidence items for legal compliance")

        return next_steps[:10]  # Limit to top 10 suggestions

    def export_evidence_package(self, format: str = "json") -> str:
        """Export complete evidence package for legal proceedings."""
        return self.evidence_collector.export_evidence_package(format)

    def get_audit_trail(self) -> List[Dict[str, Any]]:
        """Get complete audit trail for the investigation."""
        return self.audit_trail.get_all_events()

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the investigation."""
        current_time = datetime.now()
        total_duration = (current_time - self.start_time).total_seconds()

        metrics = self.performance_metrics.copy()
        metrics.update({
            "investigation_duration": total_duration,
            "memory_usage": self._get_memory_usage(),
            "cache_hit_rate": metrics["cache_hits"] / max(metrics["cache_hits"] + metrics["cache_misses"], 1),
            "error_rate": metrics["errors"] / max(metrics["api_calls"], 1),
        })

        return metrics

    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage statistics."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                "rss_mb": memory_info.rss / 1024 / 1024,
                "vms_mb": memory_info.vms / 1024 / 1024,
                "percent": process.memory_percent(),
            }
        except ImportError:
            return {"error": "psutil not available"}
        except Exception as e:
            return {"error": str(e)}

    def cleanup(self) -> None:
        """Clean up resources and finalize investigation."""
        try:
            # Close API client connections
            if hasattr(self.api_client, 'close'):
                asyncio.run(self.api_client.close())

            # Finalize audit trail
            self.audit_trail.log_event("investigation_finalized", {
                "final_metrics": self.get_performance_metrics(),
                "cleanup_timestamp": datetime.now().isoformat()
            })

            logger.info(f"Investigation {self.investigation_id} cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.cleanup()

        if exc_type:
            logger.error(f"Investigation failed with exception: {exc_type.__name__}: {exc_val}")
            self.audit_trail.log_event("investigation_failed", {
                "exception_type": exc_type.__name__,
                "exception_message": str(exc_val),
                "timestamp": datetime.now().isoformat()
            })

        return False  # Don't suppress exceptions
