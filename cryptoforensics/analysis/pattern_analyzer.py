"""
Advanced pattern analysis for cryptocurrency transactions

Provides sophisticated pattern recognition, clustering algorithms,
and behavioral analysis for cryptocurrency investigations.
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass
import statistics

from ..core.config import InvestigationConfig
from ..core.investigator import TransactionInfo
from .. import AnalysisError

logger = logging.getLogger(__name__)

@dataclass
class PatternResult:
    """Result container for pattern analysis."""
    pattern_type: str
    confidence: float
    description: str
    evidence: Dict[str, Any]
    risk_level: str
    recommendations: List[str]

@dataclass
class ClusterResult:
    """Result container for clustering analysis."""
    cluster_id: str
    addresses: List[str]
    transactions: List[str]
    total_amount: float
    cluster_type: str
    confidence: float
    characteristics: Dict[str, Any]

class PatternAnalyzer:
    """
    Advanced pattern analysis engine for cryptocurrency transactions.

    Provides sophisticated pattern recognition including timing analysis,
    amount patterns, address clustering, and behavioral fingerprinting.
    """

    def __init__(self, config: InvestigationConfig):
        """
        Initialize pattern analyzer.

        Args:
            config: Investigation configuration
        """
        self.config = config
        self.patterns_detected: List[PatternResult] = []
        self.clusters_identified: List[ClusterResult] = []

        logger.info("Pattern analyzer initialized")

    async def analyze_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """
        Perform comprehensive pattern analysis asynchronously.

        Args:
            transactions: List of transactions to analyze

        Returns:
            Dictionary containing all pattern analysis results

        Raises:
            AnalysisError: If analysis fails
        """
        try:
            if not transactions:
                return {"status": "no_data", "patterns": []}

            logger.info(f"Starting pattern analysis on {len(transactions)} transactions")

            # Run analysis components concurrently
            analysis_tasks = [
                self._analyze_timing_patterns_async(transactions),
                self._analyze_amount_patterns_async(transactions),
                self._analyze_address_patterns_async(transactions),
                self._analyze_transaction_structure_async(transactions),
                self._perform_clustering_analysis_async(transactions),
                self._analyze_behavioral_patterns_async(transactions)
            ]

            results = await asyncio.gather(*analysis_tasks, return_exceptions=True)

            # Combine results
            combined_results = {
                "timing_patterns": results[0] if not isinstance(results[0], Exception) else {},
                "amount_patterns": results[1] if not isinstance(results[1], Exception) else {},
                "address_patterns": results[2] if not isinstance(results[2], Exception) else {},
                "structure_patterns": results[3] if not isinstance(results[3], Exception) else {},
                "clustering_analysis": results[4] if not isinstance(results[4], Exception) else {},
                "behavioral_patterns": results[5] if not isinstance(results[5], Exception) else {},
                "summary": self._generate_pattern_summary(),
                "analysis_metadata": {
                    "total_transactions": len(transactions),
                    "analysis_timestamp": datetime.now().isoformat(),
                    "patterns_detected": len(self.patterns_detected),
                    "clusters_identified": len(self.clusters_identified)
                }
            }

            logger.info(f"Pattern analysis completed: {len(self.patterns_detected)} patterns detected")
            return combined_results

        except Exception as e:
            logger.error(f"Pattern analysis failed: {e}")
            raise AnalysisError(f"Pattern analysis failed: {e}")

    async def _analyze_timing_patterns_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze timing patterns in transactions."""
        if len(transactions) < 2:
            return {"status": "insufficient_data"}

        try:
            # Parse timestamps
            timestamps = []
            for tx in transactions:
                try:
                    ts = datetime.fromisoformat(tx.timestamp.replace('Z', '+00:00'))
                    timestamps.append(ts)
                except:
                    continue

            if len(timestamps) < 2:
                return {"status": "invalid_timestamps"}

            # Calculate intervals
            intervals = []
            for i in range(1, len(timestamps)):
                interval = (timestamps[i] - timestamps[i-1]).total_seconds()
                intervals.append(interval)

            # Statistical analysis
            avg_interval = statistics.mean(intervals)
            median_interval = statistics.median(intervals)
            std_interval = statistics.stdev(intervals) if len(intervals) > 1 else 0

            # Pattern detection
            patterns = {}

            # Rapid succession pattern
            rapid_threshold = 300  # 5 minutes
            rapid_count = sum(1 for interval in intervals if interval < rapid_threshold)
            if rapid_count > len(intervals) * 0.5:
                pattern = PatternResult(
                    pattern_type="rapid_succession",
                    confidence=rapid_count / len(intervals),
                    description="High frequency of rapid transactions detected",
                    evidence={"rapid_transactions": rapid_count, "total_intervals": len(intervals)},
                    risk_level="MEDIUM",
                    recommendations=["Investigate for automated trading or bot activity"]
                )
                self.patterns_detected.append(pattern)
                patterns["rapid_succession"] = True

            # Regular interval pattern (potential automation)
            if std_interval < avg_interval * 0.1 and len(intervals) > 5:
                pattern = PatternResult(
                    pattern_type="regular_intervals",
                    confidence=1.0 - (std_interval / avg_interval),
                    description="Highly regular transaction intervals detected",
                    evidence={"avg_interval": avg_interval, "std_deviation": std_interval},
                    risk_level="HIGH",
                    recommendations=["Strong indication of automated/scripted activity"]
                )
                self.patterns_detected.append(pattern)
                patterns["regular_intervals"] = True

            # Time-of-day clustering
            hours = [ts.hour for ts in timestamps]
            hour_distribution = Counter(hours)
            max_hour_count = max(hour_distribution.values())
            if max_hour_count > len(timestamps) * 0.6:
                dominant_hour = max(hour_distribution, key=hour_distribution.get)
                pattern = PatternResult(
                    pattern_type="time_clustering",
                    confidence=max_hour_count / len(timestamps),
                    description=f"Transactions clustered around hour {dominant_hour}",
                    evidence={"dominant_hour": dominant_hour, "concentration": max_hour_count / len(timestamps)},
                    risk_level="LOW",
                    recommendations=["Consider timezone analysis and user behavior patterns"]
                )
                self.patterns_detected.append(pattern)
                patterns["time_clustering"] = True

            return {
                "average_interval_seconds": avg_interval,
                "median_interval_seconds": median_interval,
                "interval_std_deviation": std_interval,
                "rapid_succession_count": rapid_count,
                "patterns_detected": patterns,
                "hour_distribution": dict(hour_distribution),
                "timing_regularity_score": 1.0 - min(1.0, std_interval / max(avg_interval, 1))
            }

        except Exception as e:
            logger.error(f"Timing pattern analysis failed: {e}")
            return {"status": "analysis_failed", "error": str(e)}

    async def _analyze_amount_patterns_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze amount patterns for suspicious activity."""
        amounts = [tx.amount_btc for tx in transactions if tx.amount_btc > 0]

        if not amounts:
            return {"status": "no_amounts"}

        try:
            # Statistical analysis
            total_amount = sum(amounts)
            avg_amount = statistics.mean(amounts)
            median_amount = statistics.median(amounts)
            std_amount = statistics.stdev(amounts) if len(amounts) > 1 else 0

            patterns = {}

            # Round number detection (potential structuring)
            round_amounts = []
            for amount in amounts:
                # Check for round numbers at different decimal places
                for precision in [0, 1, 2, 3]:
                    if abs(amount - round(amount, precision)) < 1e-8:
                        round_amounts.append(amount)
                        break

            round_ratio = len(round_amounts) / len(amounts)
            if round_ratio > 0.3:
                pattern = PatternResult(
                    pattern_type="round_amounts",
                    confidence=round_ratio,
                    description="High proportion of round number amounts",
                    evidence={"round_count": len(round_amounts), "total_count": len(amounts)},
                    risk_level="MEDIUM",
                    recommendations=["Investigate for potential structuring or layering"]
                )
                self.patterns_detected.append(pattern)
                patterns["round_amounts"] = True

            # Similar amount clustering
            amount_clusters = self._cluster_similar_amounts(amounts)
            large_clusters = [cluster for cluster in amount_clusters if len(cluster) > 2]

            if large_clusters:
                pattern = PatternResult(
                    pattern_type="amount_clustering",
                    confidence=len(large_clusters) / max(len(amounts) / 3, 1),
                    description="Multiple transactions with similar amounts",
                    evidence={"clusters": len(large_clusters), "largest_cluster": max(len(c) for c in large_clusters)},
                    risk_level="MEDIUM",
                    recommendations=["Investigate for splitting or layering techniques"]
                )
                self.patterns_detected.append(pattern)
                patterns["amount_clustering"] = True

            # Decreasing amount pattern (peel chain)
            if len(amounts) >= 3:
                decreasing_count = 0
                for i in range(1, len(amounts)):
                    if amounts[i] < amounts[i-1] * 0.9:  # 10% decrease threshold
                        decreasing_count += 1

                if decreasing_count > len(amounts) * 0.6:
                    pattern = PatternResult(
                        pattern_type="peel_chain",
                        confidence=decreasing_count / (len(amounts) - 1),
                        description="Decreasing amount pattern suggesting peel chain",
                        evidence={"decreasing_transactions": decreasing_count},
                        risk_level="HIGH",
                        recommendations=["Strong indication of peel chain money laundering technique"]
                    )
                    self.patterns_detected.append(pattern)
                    patterns["peel_chain"] = True

            # Benford's Law analysis for natural distribution
            benford_score = self._analyze_benfords_law(amounts)

            return {
                "total_amount": total_amount,
                "average_amount": avg_amount,
                "median_amount": median_amount,
                "amount_std_deviation": std_amount,
                "round_amounts_ratio": round_ratio,
                "amount_clusters": len(large_clusters),
                "patterns_detected": patterns,
                "benfords_law_score": benford_score,
                "amount_distribution": self._calculate_amount_distribution(amounts)
            }

        except Exception as e:
            logger.error(f"Amount pattern analysis failed: {e}")
            return {"status": "analysis_failed", "error": str(e)}

    def _cluster_similar_amounts(self, amounts: List[float], threshold: float = 0.001) -> List[List[float]]:
        """Cluster similar amounts together."""
        clusters = []
        used_indices = set()

        for i, amount1 in enumerate(amounts):
            if i in used_indices:
                continue

            cluster = [amount1]
            used_indices.add(i)

            for j, amount2 in enumerate(amounts[i+1:], i+1):
                if j in used_indices:
                    continue

                if abs(amount1 - amount2) <= threshold:
                    cluster.append(amount2)
                    used_indices.add(j)

            if len(cluster) > 1:
                clusters.append(cluster)

        return clusters

    def _analyze_benfords_law(self, amounts: List[float]) -> float:
        """Analyze amounts against Benford's Law for natural distribution."""
        if len(amounts) < 10:
            return 0.0

        try:
            # Extract first digits
            first_digits = []
            for amount in amounts:
                if amount > 0:
                    first_digit = int(str(amount).replace('.', '')[0])
                    if 1 <= first_digit <= 9:
                        first_digits.append(first_digit)

            if len(first_digits) < 10:
                return 0.0

            # Calculate observed distribution
            digit_counts = Counter(first_digits)
            total_count = len(first_digits)
            observed_dist = [digit_counts.get(d, 0) / total_count for d in range(1, 10)]

            # Benford's Law expected distribution
            expected_dist = [np.log10(1 + 1/d) for d in range(1, 10)]

            # Calculate chi-square goodness of fit
            chi_square = sum((obs - exp)**2 / exp for obs, exp in zip(observed_dist, expected_dist))

            # Convert to score (0-1, where 1 is perfect fit)
            # Chi-square critical value for 8 degrees of freedom at 0.05 significance is ~15.5
            benford_score = max(0, 1 - chi_square / 15.5)

            return benford_score

        except Exception as e:
            logger.error(f"Benford's Law analysis failed: {e}")
            return 0.0

    def _calculate_amount_distribution(self, amounts: List[float]) -> Dict[str, Any]:
        """Calculate amount distribution statistics."""
        if not amounts:
            return {}

        # Create amount ranges
        max_amount = max(amounts)
        ranges = {
            "micro": [a for a in amounts if a < 0.001],
            "small": [a for a in amounts if 0.001 <= a < 0.1],
            "medium": [a for a in amounts if 0.1 <= a < 1.0],
            "large": [a for a in amounts if 1.0 <= a < 10.0],
            "very_large": [a for a in amounts if a >= 10.0]
        }

        return {
            "range_distribution": {k: len(v) for k, v in ranges.items()},
            "percentiles": {
                "p25": np.percentile(amounts, 25),
                "p50": np.percentile(amounts, 50),
                "p75": np.percentile(amounts, 75),
                "p90": np.percentile(amounts, 90),
                "p95": np.percentile(amounts, 95)
            },
            "outliers": self._detect_amount_outliers(amounts)
        }

    def _detect_amount_outliers(self, amounts: List[float]) -> List[float]:
        """Detect outlier amounts using IQR method."""
        if len(amounts) < 4:
            return []

        q1 = np.percentile(amounts, 25)
        q3 = np.percentile(amounts, 75)
        iqr = q3 - q1

        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr

        outliers = [a for a in amounts if a < lower_bound or a > upper_bound]
        return outliers

    async def _analyze_address_patterns_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze address usage patterns."""
        try:
            from_addresses = [tx.from_address for tx in transactions]
            to_addresses = [tx.to_address for tx in transactions]
            all_addresses = from_addresses + to_addresses

            # Address reuse analysis
            from_counts = Counter(from_addresses)
            to_counts = Counter(to_addresses)

            # Find heavily reused addresses
            reused_from = {addr: count for addr, count in from_counts.items() if count > 1}
            reused_to = {addr: count for addr, count in to_counts.items() if count > 1}

            patterns = {}

            # High address reuse pattern
            total_reuse = len(reused_from) + len(reused_to)
            if total_reuse > len(set(all_addresses)) * 0.3:
                pattern = PatternResult(
                    pattern_type="high_address_reuse",
                    confidence=total_reuse / len(set(all_addresses)),
                    description="High level of address reuse detected",
                    evidence={"reused_addresses": total_reuse, "unique_addresses": len(set(all_addresses))},
                    risk_level="LOW",
                    recommendations=["Poor privacy practices, potential for address clustering"]
                )
                self.patterns_detected.append(pattern)
                patterns["high_address_reuse"] = True

            # Address type analysis
            address_types = self._classify_address_types(all_addresses)

            return {
                "unique_from_addresses": len(set(from_addresses)),
                "unique_to_addresses": len(set(to_addresses)),
                "total_unique_addresses": len(set(all_addresses)),
                "reused_from_addresses": len(reused_from),
                "reused_to_addresses": len(reused_to),
                "address_reuse_ratio": total_reuse / len(set(all_addresses)),
                "address_types": address_types,
                "patterns_detected": patterns,
                "most_active_addresses": {
                    "from": dict(from_counts.most_common(5)),
                    "to": dict(to_counts.most_common(5))
                }
            }

        except Exception as e:
            logger.error(f"Address pattern analysis failed: {e}")
            return {"status": "analysis_failed", "error": str(e)}

    def _classify_address_types(self, addresses: List[str]) -> Dict[str, int]:
        """Classify addresses by type (Legacy, SegWit, Taproot)."""
        types = {"legacy": 0, "segwit_v0": 0, "taproot": 0, "unknown": 0}

        for addr in addresses:
            if addr.startswith('1'):
                types["legacy"] += 1
            elif addr.startswith('3'):
                types["legacy"] += 1  # P2SH
            elif addr.startswith('bc1q'):
                types["segwit_v0"] += 1
            elif addr.startswith('bc1p'):
                types["taproot"] += 1
            else:
                types["unknown"] += 1

        return types

    async def _analyze_transaction_structure_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze transaction structure patterns."""
        try:
            # Input/output analysis
            input_counts = [tx.input_count for tx in transactions if tx.input_count > 0]
            output_counts = [tx.output_count for tx in transactions if tx.output_count > 0]

            patterns = {}

            # Many-to-one pattern (consolidation)
            high_input_txs = [tx for tx in transactions if tx.input_count > 5]
            if len(high_input_txs) > len(transactions) * 0.2:
                pattern = PatternResult(
                    pattern_type="consolidation",
                    confidence=len(high_input_txs) / len(transactions),
                    description="High number of consolidation transactions",
                    evidence={"consolidation_txs": len(high_input_txs)},
                    risk_level="LOW",
                    recommendations=["Potential wallet consolidation or exchange activity"]
                )
                self.patterns_detected.append(pattern)
                patterns["consolidation"] = True

            # One-to-many pattern (distribution)
            high_output_txs = [tx for tx in transactions if tx.output_count > 5]
            if len(high_output_txs) > len(transactions) * 0.2:
                pattern = PatternResult(
                    pattern_type="distribution",
                    confidence=len(high_output_txs) / len(transactions),
                    description="High number of distribution transactions",
                    evidence={"distribution_txs": len(high_output_txs)},
                    risk_level="MEDIUM",
                    recommendations=["Potential mixing service or mass payout activity"]
                )
                self.patterns_detected.append(pattern)
                patterns["distribution"] = True

            return {
                "average_inputs": statistics.mean(input_counts) if input_counts else 0,
                "average_outputs": statistics.mean(output_counts) if output_counts else 0,
                "max_inputs": max(input_counts) if input_counts else 0,
                "max_outputs": max(output_counts) if output_counts else 0,
                "consolidation_transactions": len(high_input_txs),
                "distribution_transactions": len(high_output_txs),
                "patterns_detected": patterns
            }

        except Exception as e:
            logger.error(f"Transaction structure analysis failed: {e}")
            return {"status": "analysis_failed", "error": str(e)}

    async def _perform_clustering_analysis_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Perform advanced clustering analysis on addresses and transactions."""
        try:
            # Build address graph
            address_graph = defaultdict(set)
            address_amounts = defaultdict(float)

            for tx in transactions:
                address_graph[tx.from_address].add(tx.to_address)
                address_amounts[tx.from_address] += tx.amount_btc
                address_amounts[tx.to_address] += tx.amount_btc

            # Find connected components (potential wallet clusters)
            clusters = self._find_address_clusters(address_graph)

            # Analyze clusters
            cluster_results = []
            for i, cluster_addresses in enumerate(clusters):
                if len(cluster_addresses) > 1:
                    cluster_txs = [tx for tx in transactions
                                 if tx.from_address in cluster_addresses or tx.to_address in cluster_addresses]

                    total_amount = sum(address_amounts[addr] for addr in cluster_addresses)

                    cluster_result = ClusterResult(
                        cluster_id=f"cluster_{i}",
                        addresses=list(cluster_addresses),
                        transactions=[tx.txid for tx in cluster_txs],
                        total_amount=total_amount,
                        cluster_type=self._classify_cluster_type(cluster_addresses, cluster_txs),
                        confidence=self._calculate_cluster_confidence(cluster_addresses, cluster_txs),
                        characteristics=self._analyze_cluster_characteristics(cluster_addresses, cluster_txs)
                    )

                    cluster_results.append(cluster_result)
                    self.clusters_identified.append(cluster_result)

            return {
                "total_clusters": len(cluster_results),
                "significant_clusters": len([c for c in cluster_results if len(c.addresses) > 2]),
                "largest_cluster_size": max(len(c.addresses) for c in cluster_results) if cluster_results else 0,
                "cluster_details": [
                    {
                        "cluster_id": c.cluster_id,
                        "address_count": len(c.addresses),
                        "transaction_count": len(c.transactions),
                        "total_amount": c.total_amount,
                        "cluster_type": c.cluster_type,
                        "confidence": c.confidence
                    }
                    for c in cluster_results
                ],
                "clustering_coefficient": self._calculate_clustering_coefficient(address_graph)
            }

        except Exception as e:
            logger.error(f"Clustering analysis failed: {e}")
            return {"status": "analysis_failed", "error": str(e)}

    def _find_address_clusters(self, address_graph: Dict[str, set]) -> List[set]:
        """Find connected components in the address graph."""
        visited = set()
        clusters = []

        def dfs(node, cluster):
            if node in visited:
                return
            visited.add(node)
            cluster.add(node)

            # Visit connected nodes
            for neighbor in address_graph.get(node, set()):
                dfs(neighbor, cluster)

            # Also check reverse connections
            for source, targets in address_graph.items():
                if node in targets and source not in visited:
                    dfs(source, cluster)

        for node in address_graph:
            if node not in visited:
                cluster = set()
                dfs(node, cluster)
                if len(cluster) > 1:
                    clusters.append(cluster)

        return clusters

    def _classify_cluster_type(self, addresses: List[str], transactions: List[TransactionInfo]) -> str:
        """Classify the type of address cluster."""
        if len(addresses) < 2:
            return "single_address"

        # Analyze transaction patterns
        input_addresses = set(tx.from_address for tx in transactions)
        output_addresses = set(tx.to_address for tx in transactions)

        cluster_set = set(addresses)

        # Check if cluster is primarily receiving
        if len(cluster_set & output_addresses) > len(cluster_set & input_addresses) * 2:
            return "receiving_cluster"

        # Check if cluster is primarily sending
        if len(cluster_set & input_addresses) > len(cluster_set & output_addresses) * 2:
            return "sending_cluster"

        # Check for mixing patterns
        if len(transactions) > len(addresses) * 3:
            return "mixing_cluster"

        return "mixed_cluster"

    def _calculate_cluster_confidence(self, addresses: List[str], transactions: List[TransactionInfo]) -> float:
        """Calculate confidence score for cluster identification."""
        if len(addresses) < 2:
            return 0.0

        # Factors that increase confidence:
        # 1. Multiple transactions between cluster addresses
        # 2. Similar transaction patterns
        # 3. Temporal clustering

        confidence_factors = []

        # Internal transaction ratio
        cluster_set = set(addresses)
        internal_txs = sum(1 for tx in transactions
                          if tx.from_address in cluster_set and tx.to_address in cluster_set)
        if transactions:
            internal_ratio = internal_txs / len(transactions)
            confidence_factors.append(internal_ratio)

        # Address reuse factor
        address_usage = Counter(tx.from_address for tx in transactions) + Counter(tx.to_address for tx in transactions)
        reuse_score = sum(1 for addr in addresses if address_usage[addr] > 1) / len(addresses)
        confidence_factors.append(reuse_score)

        # Temporal clustering
        if len(transactions) > 1:
            timestamps = [datetime.fromisoformat(tx.timestamp.replace('Z', '+00:00')) for tx in transactions]
            time_span = (max(timestamps) - min(timestamps)).total_seconds()
            if time_span < 3600:  # Within 1 hour
                confidence_factors.append(0.8)
            elif time_span < 86400:  # Within 1 day
                confidence_factors.append(0.5)
            else:
                confidence_factors.append(0.2)

        return statistics.mean(confidence_factors) if confidence_factors else 0.0

    def _analyze_cluster_characteristics(self, addresses: List[str], transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze characteristics of an address cluster."""
        return {
            "address_count": len(addresses),
            "transaction_count": len(transactions),
            "total_volume": sum(tx.amount_btc for tx in transactions),
            "average_transaction_size": statistics.mean([tx.amount_btc for tx in transactions]) if transactions else 0,
            "time_span": self._calculate_time_span(transactions),
            "address_types": self._classify_address_types(addresses),
            "activity_pattern": self._analyze_activity_pattern(transactions)
        }

    def _calculate_time_span(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Calculate time span of transactions."""
        if not transactions:
            return {"duration": 0, "start": None, "end": None}

        timestamps = [datetime.fromisoformat(tx.timestamp.replace('Z', '+00:00')) for tx in transactions]
        start_time = min(timestamps)
        end_time = max(timestamps)
        duration = (end_time - start_time).total_seconds()

        return {
            "duration_seconds": duration,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat()
        }

    def _analyze_activity_pattern(self, transactions: List[TransactionInfo]) -> str:
        """Analyze the activity pattern of transactions."""
        if len(transactions) < 3:
            return "insufficient_data"

        # Analyze transaction frequency over time
        timestamps = [datetime.fromisoformat(tx.timestamp.replace('Z', '+00:00')) for tx in transactions]
        timestamps.sort()

        intervals = [(timestamps[i] - timestamps[i-1]).total_seconds() for i in range(1, len(timestamps))]

        if not intervals:
            return "single_transaction"

        avg_interval = statistics.mean(intervals)
        std_interval = statistics.stdev(intervals) if len(intervals) > 1 else 0

        # Classify pattern
        if std_interval < avg_interval * 0.2:
            return "regular_activity"
        elif avg_interval < 300:  # Less than 5 minutes
            return "burst_activity"
        elif avg_interval > 86400:  # More than 1 day
            return "sporadic_activity"
        else:
            return "normal_activity"

    def _calculate_clustering_coefficient(self, address_graph: Dict[str, set]) -> float:
        """Calculate clustering coefficient of the address graph."""
        if len(address_graph) < 3:
            return 0.0

        total_coefficient = 0
        node_count = 0

        for node in address_graph:
            neighbors = address_graph[node]
            if len(neighbors) < 2:
                continue

            # Count triangles
            triangles = 0
            possible_triangles = len(neighbors) * (len(neighbors) - 1) // 2

            for neighbor1 in neighbors:
                for neighbor2 in neighbors:
                    if neighbor1 != neighbor2 and neighbor2 in address_graph.get(neighbor1, set()):
                        triangles += 1

            triangles //= 2  # Each triangle counted twice

            if possible_triangles > 0:
                total_coefficient += triangles / possible_triangles
                node_count += 1

        return total_coefficient / node_count if node_count > 0 else 0.0

    async def _analyze_behavioral_patterns_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze behavioral patterns in transaction data."""
        try:
            patterns = {}

            # Fee analysis
            fees = [tx.fee for tx in transactions if tx.fee is not None and tx.fee > 0]
            if fees:
                avg_fee = statistics.mean(fees)
                fee_consistency = 1.0 - (statistics.stdev(fees) / avg_fee) if len(fees) > 1 and avg_fee > 0 else 0

                if fee_consistency > 0.8:
                    pattern = PatternResult(
                        pattern_type="consistent_fees",
                        confidence=fee_consistency,
                        description="Highly consistent fee patterns detected",
                        evidence={"fee_consistency": fee_consistency, "average_fee": avg_fee},
                        risk_level="MEDIUM",
                        recommendations=["Potential automated wallet or service usage"]
                    )
                    self.patterns_detected.append(pattern)
                    patterns["consistent_fees"] = True

            # Depth pattern analysis
            depths = [tx.depth for tx in transactions]
            if depths:
                depth_distribution = Counter(depths)
                max_depth_count = max(depth_distribution.values())

                if max_depth_count > len(transactions) * 0.7:
                    dominant_depth = max(depth_distribution, key=depth_distribution.get)
                    pattern = PatternResult(
                        pattern_type="depth_clustering",
                        confidence=max_depth_count / len(transactions),
                        description=f"Transactions clustered at depth {dominant_depth}",
                        evidence={"dominant_depth": dominant_depth, "concentration": max_depth_count / len(transactions)},
                        risk_level="LOW",
                        recommendations=["Investigate transaction discovery methodology"]
                    )
                    self.patterns_detected.append(pattern)
                    patterns["depth_clustering"] = True

            return {
                "fee_analysis": {
                    "average_fee": statistics.mean(fees) if fees else 0,
                    "fee_consistency": fee_consistency if fees else 0,
                    "fee_range": {"min": min(fees), "max": max(fees)} if fees else {}
                },
                "depth_analysis": {
                    "depth_distribution": dict(depth_distribution),
                    "max_depth": max(depths) if depths else 0,
                    "average_depth": statistics.mean(depths) if depths else 0
                },
                "patterns_detected": patterns
            }

        except Exception as e:
            logger.error(f"Behavioral pattern analysis failed: {e}")
            return {"status": "analysis_failed", "error": str(e)}

    def _generate_pattern_summary(self) -> Dict[str, Any]:
        """Generate a summary of all detected patterns."""
        if not self.patterns_detected:
            return {"status": "no_patterns_detected"}

        # Group patterns by risk level
        risk_groups = defaultdict(list)
        for pattern in self.patterns_detected:
            risk_groups[pattern.risk_level].append(pattern)

        # Calculate overall risk score
        risk_weights = {"LOW": 1, "MEDIUM": 3, "HIGH": 5, "CRITICAL": 10}
        total_risk_score = sum(risk_weights.get(pattern.risk_level, 0) * pattern.confidence
                              for pattern in self.patterns_detected)

        # Generate recommendations
        all_recommendations = []
        for pattern in self.patterns_detected:
            all_recommendations.extend(pattern.recommendations)

        unique_recommendations = list(set(all_recommendations))

        return {
            "total_patterns": len(self.patterns_detected),
            "risk_distribution": {level: len(patterns) for level, patterns in risk_groups.items()},
            "overall_risk_score": total_risk_score,
            "high_confidence_patterns": len([p for p in self.patterns_detected if p.confidence > 0.7]),
            "pattern_types": list(set(p.pattern_type for p in self.patterns_detected)),
            "recommendations": unique_recommendations[:10],  # Top 10 recommendations
            "analysis_quality": self._assess_analysis_quality()
        }

    def _assess_analysis_quality(self) -> Dict[str, Any]:
        """Assess the quality of the pattern analysis."""
        quality_factors = []

        # Pattern diversity
        pattern_types = set(p.pattern_type for p in self.patterns_detected)
        if len(pattern_types) > 3:
            quality_factors.append(0.8)
        elif len(pattern_types) > 1:
            quality_factors.append(0.6)
        else:
            quality_factors.append(0.3)

        # Confidence levels
        avg_confidence = statistics.mean([p.confidence for p in self.patterns_detected]) if self.patterns_detected else 0
        quality_factors.append(avg_confidence)

        # Cluster analysis quality
        if self.clusters_identified:
            cluster_quality = statistics.mean([c.confidence for c in self.clusters_identified])
            quality_factors.append(cluster_quality)

        overall_quality = statistics.mean(quality_factors) if quality_factors else 0

        return {
            "overall_score": overall_quality,
            "pattern_diversity": len(pattern_types),
            "average_confidence": avg_confidence,
            "analysis_completeness": min(1.0, len(self.patterns_detected) / 5),  # Expect ~5 patterns for complete analysis
            "quality_level": "HIGH" if overall_quality > 0.7 else "MEDIUM" if overall_quality > 0.4 else "LOW"
        }
