"""
Suspicious activity detection for CryptoForensics

Provides advanced detection of suspicious patterns and activities
in cryptocurrency transactions using machine learning and heuristics.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from collections import Counter, defaultdict

from ..core.config import InvestigationConfig
from ..core.investigator import TransactionInfo
from .. import AnalysisError

logger = logging.getLogger(__name__)

class SuspiciousActivityDetector:
    """
    Advanced suspicious activity detection engine.
    
    Detects various types of suspicious activities including mixing services,
    exchange patterns, peel chains, and other money laundering techniques.
    """
    
    def __init__(self, config: InvestigationConfig):
        """Initialize suspicious activity detector."""
        self.config = config
        self.detected_activities: List[Dict[str, Any]] = []
        
        logger.info("Suspicious activity detector initialized")
    
    async def detect_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """
        Detect suspicious activities asynchronously.
        
        Args:
            transactions: List of transactions to analyze
            
        Returns:
            Suspicious activity detection results
        """
        if not transactions:
            return {"status": "no_data", "activities": []}
        
        try:
            # Run detection algorithms
            detection_tasks = [
                self._detect_mixing_services(transactions),
                self._detect_exchange_patterns(transactions),
                self._detect_peel_chains(transactions),
                self._detect_consolidation_patterns(transactions),
                self._detect_layering_patterns(transactions)
            ]
            
            results = await asyncio.gather(*detection_tasks, return_exceptions=True)
            
            # Combine results
            activities = {}
            for i, result in enumerate(results):
                if not isinstance(result, Exception):
                    activity_name = [
                        "mixing_services", "exchange_patterns", "peel_chains",
                        "consolidation_patterns", "layering_patterns"
                    ][i]
                    activities[activity_name] = result
            
            # Calculate overall suspicion score
            suspicion_score = sum(
                activity.get("severity", 0) for activity in activities.values()
                if activity.get("detected", False)
            )
            
            return {
                "activities": activities,
                "overall_suspicion_score": suspicion_score,
                "suspicion_level": self._classify_suspicion_level(suspicion_score),
                "detected_count": sum(1 for a in activities.values() if a.get("detected", False)),
                "analysis_metadata": {
                    "total_transactions": len(transactions),
                    "analysis_timestamp": "2024-01-01T00:00:00Z"  # Placeholder
                }
            }
            
        except Exception as e:
            logger.error(f"Suspicious activity detection failed: {e}")
            raise AnalysisError(f"Suspicious activity detection failed: {e}")
    
    async def _detect_mixing_services(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect potential mixing service usage."""
        mixing_indicators = 0
        evidence = {}
        
        # Look for multiple small outputs (typical mixing pattern)
        small_outputs = [tx for tx in transactions if tx.amount_btc < 0.01]
        if len(small_outputs) > len(transactions) * 0.6:
            mixing_indicators += 1
            evidence["small_outputs_ratio"] = len(small_outputs) / len(transactions)
        
        # Look for round amounts
        round_amounts = [tx for tx in transactions if tx.amount_btc == round(tx.amount_btc, 8)]
        if len(round_amounts) > len(transactions) * 0.4:
            mixing_indicators += 1
            evidence["round_amounts_ratio"] = len(round_amounts) / len(transactions)
        
        detected = mixing_indicators >= 1
        
        return {
            "detected": detected,
            "confidence": mixing_indicators / 2.0,
            "severity": 3 if detected else 0,
            "description": "Potential mixing service usage detected" if detected else "No mixing patterns detected",
            "evidence": evidence,
            "recommendations": ["Investigate mixing service usage further"] if detected else []
        }
    
    async def _detect_exchange_patterns(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect potential exchange deposit/withdrawal patterns."""
        # Look for addresses that receive from multiple sources
        to_address_sources = defaultdict(set)
        for tx in transactions:
            to_address_sources[tx.to_address].add(tx.from_address)
        
        # Find addresses receiving from multiple sources
        potential_exchanges = {
            addr: len(sources) for addr, sources in to_address_sources.items()
            if len(sources) > 2
        }
        
        detected = len(potential_exchanges) > 0
        
        return {
            "detected": detected,
            "confidence": min(1.0, len(potential_exchanges) / 3),
            "severity": 2 if detected else 0,
            "description": "Potential exchange patterns detected" if detected else "No exchange patterns detected",
            "evidence": {"potential_exchanges": len(potential_exchanges)},
            "recommendations": ["Contact exchanges for cooperation"] if detected else []
        }
    
    async def _detect_peel_chains(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect peel chain patterns."""
        if len(transactions) < 3:
            return {"detected": False, "severity": 0, "description": "Insufficient data for peel chain analysis"}
        
        # Sort by depth to analyze chain progression
        sorted_txs = sorted(transactions, key=lambda x: x.depth)
        
        # Look for decreasing amounts pattern
        decreasing_count = 0
        for i in range(1, len(sorted_txs)):
            if sorted_txs[i].amount_btc < sorted_txs[i-1].amount_btc * 0.9:
                decreasing_count += 1
        
        # Peel chain detected if most transactions show decreasing amounts
        is_peel_chain = decreasing_count > len(sorted_txs) * 0.6
        
        return {
            "detected": is_peel_chain,
            "confidence": decreasing_count / max(len(sorted_txs) - 1, 1),
            "severity": 3 if is_peel_chain else 0,
            "description": "Peel chain pattern detected" if is_peel_chain else "No peel chain pattern detected",
            "evidence": {
                "decreasing_transactions": decreasing_count,
                "total_transactions": len(sorted_txs),
                "pattern_strength": decreasing_count / max(len(sorted_txs) - 1, 1)
            },
            "recommendations": ["Strong indication of peel chain money laundering"] if is_peel_chain else []
        }
    
    async def _detect_consolidation_patterns(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect fund consolidation patterns."""
        # Look for addresses that receive from multiple sources
        consolidation_addresses = defaultdict(lambda: {"sources": set(), "total_amount": 0, "count": 0})
        
        for tx in transactions:
            consolidation_addresses[tx.to_address]["sources"].add(tx.from_address)
            consolidation_addresses[tx.to_address]["total_amount"] += tx.amount_btc
            consolidation_addresses[tx.to_address]["count"] += 1
        
        # Find significant consolidation
        significant_consolidation = {
            addr: data for addr, data in consolidation_addresses.items()
            if len(data["sources"]) > 2 and data["total_amount"] > 0.1
        }
        
        detected = len(significant_consolidation) > 0
        
        return {
            "detected": detected,
            "confidence": min(1.0, len(significant_consolidation) / 2),
            "severity": 1 if detected else 0,
            "description": "Fund consolidation patterns detected" if detected else "No consolidation patterns detected",
            "evidence": {"consolidation_addresses": len(significant_consolidation)},
            "recommendations": ["Monitor for wallet consolidation activity"] if detected else []
        }
    
    async def _detect_layering_patterns(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Detect layering patterns (complex transaction chains)."""
        # Analyze transaction depth distribution
        depth_distribution = Counter(tx.depth for tx in transactions)
        max_depth = max(depth_distribution.keys()) if depth_distribution else 0
        
        # Look for complex layering (many intermediate steps)
        complex_layering = max_depth > 7 and len(depth_distribution) > 5
        
        return {
            "detected": complex_layering,
            "confidence": min(1.0, max_depth / 10),
            "severity": 2 if complex_layering else 0,
            "description": "Complex layering patterns detected" if complex_layering else "No layering patterns detected",
            "evidence": {
                "max_depth": max_depth,
                "depth_diversity": len(depth_distribution)
            },
            "recommendations": ["Investigate complex transaction layering"] if complex_layering else []
        }
    
    def _classify_suspicion_level(self, score: int) -> str:
        """Classify overall suspicion level based on score."""
        if score >= 8:
            return "VERY_HIGH"
        elif score >= 6:
            return "HIGH"
        elif score >= 4:
            return "MEDIUM"
        elif score >= 2:
            return "LOW"
        else:
            return "MINIMAL"
