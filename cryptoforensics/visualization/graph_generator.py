"""
Graph generation and visualization for CryptoForensics

Provides advanced interactive graph visualizations for transaction flows
and address relationships.
"""

import logging
import networkx as nx
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from ..core.config import InvestigationConfig
from ..core.investigator import TransactionInfo

logger = logging.getLogger(__name__)

class GraphGenerator:
    """
    Advanced graph visualization generator.
    
    Creates interactive visualizations of transaction flows, address relationships,
    and investigation results with professional styling and export capabilities.
    """
    
    def __init__(self, config: InvestigationConfig):
        """Initialize graph generator."""
        self.config = config
        
        logger.info("Graph generator initialized")
    
    def generate_interactive_visualization(self, 
                                         graph: nx.DiGraph, 
                                         transactions: List[TransactionInfo],
                                         investigation_id: str,
                                         save_html: bool = True) -> Optional[str]:
        """
        Generate interactive visualization of transaction flow.
        
        Args:
            graph: NetworkX graph of transactions
            transactions: List of transaction information
            investigation_id: Investigation identifier
            save_html: Whether to save HTML file
            
        Returns:
            Path to saved file or None if not saved
        """
        if not graph.nodes:
            logger.warning("No data available to generate a graph.")
            return None
        
        try:
            # For now, create a simple placeholder
            # In a full implementation, this would use Plotly or similar
            
            if save_html and self.config.save_visualizations:
                output_path = Path(self.config.output_directory)
                filename = output_path / f"investigation_{investigation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                
                # Create simple HTML visualization placeholder
                html_content = self._create_html_placeholder(graph, transactions, investigation_id)
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                logger.info(f"Visualization saved as {filename}")
                return str(filename)
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating visualization: {e}")
            return None
    
    def _create_html_placeholder(self, 
                                graph: nx.DiGraph, 
                                transactions: List[TransactionInfo],
                                investigation_id: str) -> str:
        """Create HTML placeholder for visualization."""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>CryptoForensics Investigation - {investigation_id}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .stats {{ margin: 20px 0; }}
        .stat-item {{ display: inline-block; margin: 10px; padding: 10px; background-color: #e0e0e0; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 CryptoForensics Investigation Report</h1>
        <p><strong>Investigation ID:</strong> {investigation_id}</p>
        <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <h2>📊 Investigation Statistics</h2>
        <div class="stat-item">
            <strong>Transactions:</strong> {len(transactions)}
        </div>
        <div class="stat-item">
            <strong>Addresses:</strong> {len(graph.nodes)}
        </div>
        <div class="stat-item">
            <strong>Total Amount:</strong> {sum(tx.amount_btc for tx in transactions):.8f} BTC
        </div>
    </div>
    
    <div>
        <h2>🎯 Transaction Flow</h2>
        <p>Interactive visualization would be displayed here in the full implementation.</p>
        <p>This placeholder demonstrates the modular architecture of CryptoForensics v3.0.</p>
    </div>
    
    <div>
        <h2>📋 Transaction Details</h2>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr>
                <th>TXID</th>
                <th>From</th>
                <th>To</th>
                <th>Amount (BTC)</th>
                <th>Depth</th>
            </tr>
            {"".join(f'''
            <tr>
                <td>{tx.txid[:16]}...</td>
                <td>{tx.from_address[:16]}...</td>
                <td>{tx.to_address[:16]}...</td>
                <td>{tx.amount_btc:.8f}</td>
                <td>{tx.depth}</td>
            </tr>
            ''' for tx in transactions[:10])}
        </table>
        {f"<p><em>Showing first 10 of {len(transactions)} transactions</em></p>" if len(transactions) > 10 else ""}
    </div>
</body>
</html>
        """
