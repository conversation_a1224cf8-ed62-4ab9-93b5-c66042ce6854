"""
Evidence collection and management module for CryptoForensics

Provides professional-grade evidence collection, chain of custody management,
and audit trail functionality meeting legal standards.
"""

from .collector import EvidenceCollector, EvidenceItem
from .audit_trail import AuditTrail, AuditLogEntry

__all__ = [
    "EvidenceCollector",
    "EvidenceItem", 
    "AuditTrail",
    "AuditLogEntry"
]
